import {
  useAnnouncementQuery,
  useCountryCodeQuery,
  useGetMeQuery,
  useGuestQuery,
  useOfferWallQuery,
  useSettingQuery,
  useSVReferralQuery,
  useSVUserBalanceQuery,
  useSVUserQuery,
} from '@services';
import { useBAStore, useSVStore, useUserStore } from '@stores';

export function useFetchQueries(autoFetch = false) {
  const storeUser = useUserStore();
  const storeSV = useSVStore();
  const storeBA = useBAStore();

  const { isAuthenticated } = storeToRefs(storeUser);
  const { isSVAuthenticated } = storeToRefs(storeSV);

  const enabled = computed(() => autoFetch);

  // HTM Queries
  useGuestQuery({
    enabled: computed(() => !isAuthenticated.value && enabled.value),
    select: (data) => {
      storeUser.setUser(data.user);
      storeUser.setToken(data.token);
      return data;
    },
  });

  const userQuery = useGetMeQuery({
    enabled: computed(() => isAuthenticated.value && enabled.value),
    select: (data) => {
      storeUser.setUser(data);
      return data;
    },
  });

  const gameSettingQuery = useSettingQuery({
    enabled: enabled,
    select: (data) => {
      storeUser.setSettings(data);
      return data;
    },
  });

  const countryCodeQuery = useCountryCodeQuery({
    enabled: enabled,
    select: (data) => {
      storeUser.currentCountryCode = data.country;
      return data;
    },
  });

  const announcementQuery = useAnnouncementQuery({
    enabled: enabled,
    select: (data) => {
      storeUser.setNotifications(data);
      return data;
    },
  });

  const svUserSubmittedQuery = useSVReferralQuery({
    enabled: computed(() => isAuthenticated.value && enabled.value),
    select: (data) => {
      storeUser.setMerchantAcquisition({
        max_submit: data.max,
        total_submit: data.current,
      });
      return data;
    },
  });

  const offerWallQuery = useOfferWallQuery({
    enabled: enabled,
    select: (data) => {
      storeBA.setOfferWallData(data);
      return data;
    },
  });

  // Sqkii Vouchers Queries
  const svUserQuery = useSVUserQuery({
    enabled: computed(() => isSVAuthenticated.value && enabled.value),
    select: (data) => {
      storeSV.setUser(data.user);
      return data;
    },
  });

  const svUserBalanceQuery = useSVUserBalanceQuery({
    enabled: computed(() => isSVAuthenticated.value && enabled.value),
    select: (data) => {
      storeSV.setUserBalance(data.balance);
      return data;
    },
  });

  const queries = [countryCodeQuery, announcementQuery, gameSettingQuery, offerWallQuery];

  const fetched = computed(() => {
    return queries.every((query) => query.isFetched.value);
  });

  return {
    // HTM Queries
    fetched,
    userQuery,
    announcementQuery,
    gameSettingQuery,
    svUserSubmittedQuery,
    offerWallQuery,
    // Sqkii Vouchers Queries
    svUserQuery,
    svUserBalanceQuery,
  };
}
